import 'package:bloomg_flutter/auth/repository/auth_repository.dart';

/// {@template auth_repository_impl}
/// Implementation of [AuthRepository] for mock authentication.
/// {@endtemplate}
class AuthRepositoryImpl implements AuthRepository {
  /// {@macro auth_repository_impl}
  const AuthRepositoryImpl();

  @override
  Future<void> logInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    // Simulate network delay
    await Future<void>.delayed(const Duration(seconds: 2));
    
    // Mock authentication logic
    if (email.isEmpty || password.isEmpty) {
      throw const LogInWithEmailAndPasswordFailure('Invalid credentials');
    }
    
    // For demo purposes, always succeed
    // In a real app, this would make an API call
  }

  @override
  Future<void> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    // Simulate network delay
    await Future<void>.delayed(const Duration(seconds: 2));
    
    // Mock sign up logic
    if (email.isEmpty || password.isEmpty || name.isEmpty) {
      throw const SignUpWithEmailAndPasswordFailure('Invalid information');
    }
    
    // For demo purposes, always succeed
    // In a real app, this would make an API call
  }

  @override
  Future<void> sendPasswordResetEmail({
    required String email,
  }) async {
    // Simulate network delay
    await Future<void>.delayed(const Duration(seconds: 2));
    
    // Mock password reset logic
    if (email.isEmpty) {
      throw const SendPasswordResetEmailFailure('Invalid email');
    }
    
    // For demo purposes, always succeed
    // In a real app, this would make an API call
  }

  @override
  Future<void> logOut() async {
    // Simulate network delay
    await Future<void>.delayed(const Duration(milliseconds: 500));
    
    // Mock logout logic
    // In a real app, this would clear tokens, etc.
  }
}
